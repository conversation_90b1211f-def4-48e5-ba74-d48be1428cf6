#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UC账号密码统一获取脚本
一键运行所有获取脚本并统计结果到一个文件中
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path
import logging

# 导入两个获取器
from uc_account_fetcher import UCAccountFetcher
from agiso_account_fetcher import AgisoAccountFetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('all_fetchers.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AllFetchers:
    def __init__(self, output_file='all_uc_accounts.json'):
        self.output_file = Path(output_file)
        self.results = []
        
    async def run_all_fetchers(self):
        """运行所有获取器"""
        logger.info("开始运行所有UC账号获取器...")
        
        # 运行语雀获取器
        logger.info("=" * 50)
        logger.info("运行语雀获取器...")
        try:
            yuque_fetcher = UCAccountFetcher('yuque_temp.json')
            yuque_result = await yuque_fetcher.run()
            if yuque_result:
                yuque_result['source_name'] = '语雀文档'
                yuque_result['source_type'] = 'yuque'
                self.results.append(yuque_result)
                logger.info("语雀获取器运行成功")
            else:
                logger.warning("语雀获取器运行失败")
        except Exception as e:
            logger.error(f"语雀获取器运行出错: {str(e)}")
        
        # 运行阿奇索获取器
        logger.info("=" * 50)
        logger.info("运行阿奇索获取器...")
        try:
            agiso_fetcher = AgisoAccountFetcher('agiso_temp.json')
            agiso_result = await agiso_fetcher.run()
            if agiso_result:
                agiso_result['source_name'] = '阿奇索系统'
                if 'source_type' not in agiso_result:
                    agiso_result['source_type'] = 'agiso'
                self.results.append(agiso_result)
                logger.info("阿奇索获取器运行成功")
            else:
                logger.warning("阿奇索获取器运行失败")
        except Exception as e:
            logger.error(f"阿奇索获取器运行出错: {str(e)}")
        
        logger.info("=" * 50)
        logger.info(f"所有获取器运行完成，成功获取 {len(self.results)} 个账号")
        
        return self.results
    
    def save_combined_results(self):
        """保存合并后的结果"""
        try:
            if not self.results:
                logger.warning("没有获取到任何账号信息")
                return False
            
            # 准备统计数据
            combined_data = {
                'summary': {
                    'total_accounts': len(self.results),
                    'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'sources': [result['source_name'] for result in self.results]
                },
                'accounts': self.results
            }
            
            # 保存JSON格式
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(combined_data, f, ensure_ascii=False, indent=2)
            
            # 保存TXT格式
            txt_file = self.output_file.with_suffix('.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write("UC浏览器VIP账号信息汇总\n")
                f.write("=" * 40 + "\n")
                f.write(f"获取时间: {combined_data['summary']['fetch_time']}\n")
                f.write(f"账号总数: {combined_data['summary']['total_accounts']}\n")
                f.write("\n")

                for i, account in enumerate(self.results, 1):
                    f.write(f"账号 {i}:\n")
                    f.write(f"  账号: {account['account']}\n")
                    f.write(f"  密码: {account['password']}\n")
                    # 显示更新时间（语雀显示文档更新时间，阿奇索显示获取时间）
                    if account.get('source_type') == 'yuque' and 'update_time' in account:
                        f.write(f"  更新时间: {account['update_time']}\n")
                    elif account.get('source_type') == 'agiso' and 'fetch_time' in account:
                        f.write(f"  获取时间: {account['fetch_time']}\n")
                    f.write("\n")
            
            logger.info(f"统计结果已保存到: {self.output_file} 和 {txt_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存统计结果失败: {str(e)}")
            return False
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_files = [
            'yuque_temp.json', 'yuque_temp.txt',
            'agiso_temp.json', 'agiso_temp.txt'
        ]
        
        for temp_file in temp_files:
            try:
                temp_path = Path(temp_file)
                if temp_path.exists():
                    temp_path.unlink()
            except Exception as e:
                logger.warning(f"清理临时文件 {temp_file} 失败: {str(e)}")
    
    async def run(self):
        """主运行流程"""
        try:
            # 运行所有获取器
            results = await self.run_all_fetchers()
            
            # 保存统计结果
            if results:
                success = self.save_combined_results()
                if success:
                    logger.info("任务完成!")
                    return results
                else:
                    logger.error("保存统计结果失败!")
                    return None
            else:
                logger.error("所有获取器都运行失败!")
                return None
                
        finally:
            # 清理临时文件
            self.cleanup_temp_files()

def print_summary(results):
    """打印结果摘要"""
    if not results:
        print("\n❌ 获取失败，请查看日志文件")
        return
    
    print(f"\n✅ 成功获取 {len(results)} 个UC账号:")
    print("=" * 50)
    
    for i, account in enumerate(results, 1):
        print(f"账号 {i}:")
        print(f"  账号: {account['account']}")
        print(f"  密码: {account['password']}")
        # 显示时间（语雀显示文档更新时间，阿奇索显示获取时间）
        if account.get('source_type') == 'yuque' and 'update_time' in account:
            print(f"  更新时间: {account['update_time']}")
        elif account.get('source_type') == 'agiso' and 'fetch_time' in account:
            print(f"  获取时间: {account['fetch_time']}")
        print()
    
    print("详细信息已保存到 all_uc_accounts.json 和 all_uc_accounts.txt")

async def main():
    """主函数"""
    fetcher = AllFetchers()
    results = await fetcher.run()
    print_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
